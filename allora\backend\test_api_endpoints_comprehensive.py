#!/usr/bin/env python3
"""
Comprehensive API Endpoint Testing Script
========================================

Tests all REST API endpoints in the Allora backend application.
Includes authentication, CRUD operations, error handling, and performance testing.

Usage:
    python test_api_endpoints_comprehensive.py

Requirements:
    - Backend server running on http://localhost:5000
    - Redis server running (for session/cache tests)
    - MySQL database accessible
"""

import requests
import json
import time
import sys
import threading
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

class APIEndpointTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = {
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'total': 0,
            'details': [],
            'performance': {},
            'errors': []
        }
        self.auth_token = None
        self.admin_token = None
        self.seller_token = None
        
    def log_result(self, endpoint: str, method: str, status: str, 
                   response_code: int = None, message: str = "", 
                   response_time: float = None):
        """Log test result"""
        result = {
            'endpoint': endpoint,
            'method': method,
            'status': status,
            'response_code': response_code,
            'message': message,
            'response_time': response_time,
            'timestamp': datetime.now().isoformat()
        }
        self.results['details'].append(result)
        self.results['total'] += 1
        
        if status == 'PASSED':
            self.results['passed'] += 1
        elif status == 'FAILED':
            self.results['failed'] += 1
        else:
            self.results['skipped'] += 1
            
        # Log performance data
        if response_time and endpoint not in self.results['performance']:
            self.results['performance'][endpoint] = []
        if response_time:
            self.results['performance'][endpoint].append(response_time)

    def test_endpoint(self, endpoint: str, method: str = 'GET', 
                     data: dict = None, headers: dict = None,
                     expected_codes: List[int] = None, 
                     auth_required: bool = False) -> bool:
        """Test a single endpoint"""
        if expected_codes is None:
            expected_codes = [200, 201, 202]
            
        url = f"{self.base_url}{endpoint}"
        
        # Add authentication if required
        if auth_required and self.auth_token:
            if headers is None:
                headers = {}
            headers['Authorization'] = f'Bearer {self.auth_token}'
        
        try:
            start_time = time.time()
            
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, headers=headers, timeout=30)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, headers=headers, timeout=30)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, headers=headers, timeout=30)
            else:
                self.log_result(endpoint, method, 'SKIPPED', message=f'Unsupported method: {method}')
                return False
                
            response_time = time.time() - start_time
            
            if response.status_code in expected_codes:
                self.log_result(endpoint, method, 'PASSED', response.status_code, 
                              'Success', response_time)
                return True
            else:
                error_msg = f'Expected {expected_codes}, got {response.status_code}'
                try:
                    error_detail = response.json().get('error', response.text[:200])
                    error_msg += f' - {error_detail}'
                except:
                    error_msg += f' - {response.text[:200]}'
                    
                self.log_result(endpoint, method, 'FAILED', response.status_code, 
                              error_msg, response_time)
                return False
                
        except requests.exceptions.Timeout:
            self.log_result(endpoint, method, 'FAILED', message='Request timeout')
            return False
        except requests.exceptions.ConnectionError:
            self.log_result(endpoint, method, 'FAILED', message='Connection error')
            return False
        except Exception as e:
            self.log_result(endpoint, method, 'FAILED', message=f'Exception: {str(e)}')
            return False

    def setup_authentication(self):
        """Setup authentication tokens for testing"""
        print("🔐 Setting up authentication...")
        
        # Test user signup/login
        signup_data = {
            'username': 'testuser_api',
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'full_name': 'Test User API'
        }
        
        try:
            # Try to signup (might fail if user exists)
            self.session.post(f"{self.base_url}/api/signup", json=signup_data, timeout=10)
            
            # Login to get token
            login_data = {
                'email': '<EMAIL>',
                'password': 'TestPassword123!'
            }
            
            response = self.session.post(f"{self.base_url}/api/login", json=login_data, timeout=10)
            if response.status_code == 200:
                self.auth_token = response.json().get('access_token')
                print("✅ User authentication successful")
            else:
                print("⚠️  User authentication failed")
                
        except Exception as e:
            print(f"⚠️  Authentication setup error: {e}")

    def test_basic_endpoints(self):
        """Test basic endpoints that don't require authentication"""
        print("\n📋 Testing basic endpoints...")
        
        basic_endpoints = [
            ('/', 'GET'),
            ('/socketio-test', 'GET'),
            ('/api/health', 'GET'),
            ('/api/health/status', 'GET'),
            ('/api/health/database', 'GET'),
            ('/api/categories', 'GET'),
            ('/api/products', 'GET'),
            ('/api/sellers', 'GET'),
            ('/api/search/suggestions', 'GET'),
            ('/api/search/filters', 'GET'),
            ('/api/community_insights', 'GET'),
            ('/api/products/best-sellers', 'GET'),
            ('/api/products/new-arrivals', 'GET'),
            ('/api/inventory_predictions', 'GET'),
            ('/api/price_trends', 'GET'),
            ('/api/advanced_inventory_predictions', 'GET'),
            ('/api/advanced_price_trends', 'GET'),
            ('/sitemap.xml', 'GET'),
            ('/robots.txt', 'GET'),
            ('/api/content', 'GET'),
            ('/api/cache/test', 'GET'),
            ('/api/session/test', 'GET'),
            ('/api/test/sentry/status', 'GET'),
        ]
        
        for endpoint, method in basic_endpoints:
            self.test_endpoint(endpoint, method)
            time.sleep(0.1)  # Small delay to avoid overwhelming server

    def test_authentication_endpoints(self):
        """Test authentication-related endpoints"""
        print("\n🔐 Testing authentication endpoints...")
        
        # Test OAuth providers
        self.test_endpoint('/api/oauth/providers', 'GET')
        
        # Test signup with invalid data
        invalid_signup = {
            'username': '',
            'email': 'invalid-email',
            'password': '123'
        }
        self.test_endpoint('/api/signup', 'POST', data=invalid_signup, expected_codes=[400, 422])
        
        # Test login with invalid credentials
        invalid_login = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        self.test_endpoint('/api/login', 'POST', data=invalid_login, expected_codes=[401, 400])

    def test_product_endpoints(self):
        """Test product-related endpoints"""
        print("\n📦 Testing product endpoints...")
        
        # Test product listing with various parameters
        product_endpoints = [
            ('/api/products?page=1&per_page=10', 'GET'),
            ('/api/products?category=Electronics', 'GET'),
            ('/api/products?sort=price_asc', 'GET'),
            ('/api/products?min_price=100&max_price=1000', 'GET'),
            ('/api/categories/Electronics/products', 'GET'),
            ('/api/products/1', 'GET'),  # Assuming product ID 1 exists
            ('/api/products/batch', 'POST', {'product_ids': [1, 2, 3]}),
        ]
        
        for item in product_endpoints:
            if len(item) == 2:
                endpoint, method = item
                self.test_endpoint(endpoint, method)
            else:
                endpoint, method, data = item
                self.test_endpoint(endpoint, method, data=data)

    def test_authenticated_endpoints(self):
        """Test endpoints that require authentication"""
        if not self.auth_token:
            print("⚠️  Skipping authenticated endpoints - no auth token")
            return
            
        print("\n🔒 Testing authenticated endpoints...")
        
        auth_endpoints = [
            ('/api/profile', 'GET'),
            ('/api/addresses', 'GET'),
            ('/api/wishlist', 'GET'),
            ('/api/orders', 'GET'),
            ('/api/payment-methods', 'GET'),
            ('/api/cart', 'GET'),
            ('/api/recently-viewed', 'GET'),
            ('/api/recommendations', 'GET'),
            ('/api/recommendations/personalized', 'GET'),
        ]
        
        for endpoint, method in auth_endpoints:
            self.test_endpoint(endpoint, method, auth_required=True)

    def test_search_endpoints(self):
        """Test search functionality"""
        print("\n🔍 Testing search endpoints...")
        
        search_endpoints = [
            ('/api/search?q=laptop', 'GET'),
            ('/api/search/autocomplete?q=lap', 'GET'),
            ('/api/search/suggestions?q=phone', 'GET'),
            ('/api/search/similar/1', 'GET'),
            ('/api/search/health', 'GET'),
        ]
        
        for endpoint, method in search_endpoints:
            self.test_endpoint(endpoint, method)

    def run_performance_test(self, endpoint: str, concurrent_requests: int = 10):
        """Run performance test on specific endpoint"""
        print(f"\n⚡ Performance testing {endpoint} with {concurrent_requests} concurrent requests...")
        
        def make_request():
            return self.test_endpoint(endpoint, 'GET')
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(make_request) for _ in range(concurrent_requests)]
            results = [future.result() for future in as_completed(futures)]
        
        total_time = time.time() - start_time
        success_rate = sum(results) / len(results) * 100
        
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Requests/second: {concurrent_requests/total_time:.2f}")

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📊 COMPREHENSIVE API TEST REPORT")
        print("="*60)
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total Tests: {self.results['total']}")
        print(f"   Passed: {self.results['passed']} ({self.results['passed']/max(self.results['total'],1)*100:.1f}%)")
        print(f"   Failed: {self.results['failed']} ({self.results['failed']/max(self.results['total'],1)*100:.1f}%)")
        print(f"   Skipped: {self.results['skipped']} ({self.results['skipped']/max(self.results['total'],1)*100:.1f}%)")
        
        # Performance summary
        if self.results['performance']:
            print(f"\n⚡ PERFORMANCE SUMMARY:")
            for endpoint, times in self.results['performance'].items():
                if times:
                    avg_time = sum(times) / len(times)
                    max_time = max(times)
                    min_time = min(times)
                    print(f"   {endpoint}: avg={avg_time:.3f}s, min={min_time:.3f}s, max={max_time:.3f}s")
        
        # Failed tests
        failed_tests = [r for r in self.results['details'] if r['status'] == 'FAILED']
        if failed_tests:
            print(f"\n❌ FAILED TESTS ({len(failed_tests)}):")
            for test in failed_tests[:10]:  # Show first 10 failures
                print(f"   {test['method']} {test['endpoint']}: {test['message']}")
            if len(failed_tests) > 10:
                print(f"   ... and {len(failed_tests) - 10} more failures")
        
        # Save detailed report
        report_file = f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n📄 Detailed report saved to: {report_file}")

    def run_all_tests(self):
        """Run all API endpoint tests"""
        print("🚀 Starting Comprehensive API Endpoint Testing...")
        print(f"🎯 Target: {self.base_url}")
        
        start_time = time.time()
        
        # Setup
        self.setup_authentication()
        
        # Run test suites
        self.test_basic_endpoints()
        self.test_authentication_endpoints()
        self.test_product_endpoints()
        self.test_authenticated_endpoints()
        self.test_search_endpoints()
        
        # Performance tests on key endpoints
        self.run_performance_test('/api/health')
        self.run_performance_test('/api/products')
        
        total_time = time.time() - start_time
        print(f"\n⏱️  Total testing time: {total_time:.2f} seconds")
        
        # Generate report
        self.generate_report()

if __name__ == "__main__":
    tester = APIEndpointTester()
    tester.run_all_tests()
