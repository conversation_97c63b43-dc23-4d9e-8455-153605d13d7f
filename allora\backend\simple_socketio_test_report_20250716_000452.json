{"passed": 3, "failed": 5, "total": 8, "details": [{"test_name": "SocketIO Test Page", "status": "PASSED", "message": "Page accessible with SocketIO content", "timestamp": "2025-07-16T00:04:40.159297"}, {"test_name": "SocketIO Connections API", "status": "FAILED", "message": "Endpoint not found - SocketIO routes may not be registered", "timestamp": "2025-07-16T00:04:42.198640"}, {"test_name": "Inventory Broadcast API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:04:44.255214"}, {"test_name": "Price Broadcast API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:04:46.309351"}, {"test_name": "User Notification API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:04:48.349146"}, {"test_name": "Cart Update API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:04:50.419959"}, {"test_name": "SocketIO CORS Setup", "status": "PASSED", "message": "CORS headers present: ['Access-Control-Allow-Origin', 'Access-Control-Allow-Credentials']", "timestamp": "2025-07-16T00:04:52.492070"}, {"test_name": "SocketIO Manager Integration", "status": "PASSED", "message": "Server is responding normally", "timestamp": "2025-07-16T00:04:52.492070"}]}