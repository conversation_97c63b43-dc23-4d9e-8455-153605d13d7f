#!/usr/bin/env python3
"""
Simple SocketIO Testing Script
=============================

Tests SocketIO functionality with a simpler approach.
Focuses on connection testing and basic event handling.

Usage:
    python test_socketio_simple.py

Requirements:
    - Backend server running on http://localhost:5000
    - Flask-SocketIO enabled
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class SimpleSocketIOTester:
    def __init__(self, server_url: str = "http://localhost:5000"):
        self.server_url = server_url
        self.results = {
            'passed': 0,
            'failed': 0,
            'total': 0,
            'details': []
        }
        
    def log_result(self, test_name: str, status: str, message: str = ""):
        """Log test result"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.results['details'].append(result)
        self.results['total'] += 1
        
        if status == 'PASSED':
            self.results['passed'] += 1
        else:
            self.results['failed'] += 1
            
        print(f"{'✅' if status == 'PASSED' else '❌'} {test_name}: {message}")

    def test_socketio_page_access(self):
        """Test if SocketIO test page is accessible"""
        print("\n🌐 Testing SocketIO test page access...")
        
        try:
            response = requests.get(f"{self.server_url}/socketio-test", timeout=10)
            
            if response.status_code == 200:
                if 'socket.io' in response.text.lower():
                    self.log_result("SocketIO Test Page", "PASSED", "Page accessible with SocketIO content")
                else:
                    self.log_result("SocketIO Test Page", "PASSED", "Page accessible but no SocketIO content detected")
            else:
                self.log_result("SocketIO Test Page", "FAILED", f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_result("SocketIO Test Page", "FAILED", f"Error: {str(e)}")

    def test_socketio_api_endpoints(self):
        """Test SocketIO-related API endpoints"""
        print("\n🔗 Testing SocketIO API endpoints...")
        
        # Test connection stats endpoint
        try:
            response = requests.get(f"{self.server_url}/api/socketio/connections", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                self.log_result("SocketIO Connections API", "PASSED", f"Response: {data}")
            elif response.status_code == 404:
                self.log_result("SocketIO Connections API", "FAILED", "Endpoint not found - SocketIO routes may not be registered")
            else:
                self.log_result("SocketIO Connections API", "FAILED", f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_result("SocketIO Connections API", "FAILED", f"Error: {str(e)}")

    def test_broadcast_endpoints(self):
        """Test broadcast API endpoints"""
        print("\n📢 Testing broadcast endpoints...")
        
        # Test inventory update broadcast
        inventory_data = {
            'product_id': 1,
            'new_quantity': 50,
            'old_quantity': 100
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/api/socketio/events/inventory-update",
                json=inventory_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_result("Inventory Broadcast API", "PASSED", "Broadcast triggered successfully")
            elif response.status_code == 404:
                self.log_result("Inventory Broadcast API", "FAILED", "Endpoint not found")
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_detail = response.json().get('error', '')
                    if error_detail:
                        error_msg += f" - {error_detail}"
                except:
                    pass
                self.log_result("Inventory Broadcast API", "FAILED", error_msg)
                
        except Exception as e:
            self.log_result("Inventory Broadcast API", "FAILED", f"Error: {str(e)}")
        
        # Test price update broadcast
        price_data = {
            'product_id': 1,
            'new_price': 299.99,
            'old_price': 399.99
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/api/socketio/events/price-update",
                json=price_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_result("Price Broadcast API", "PASSED", "Broadcast triggered successfully")
            elif response.status_code == 404:
                self.log_result("Price Broadcast API", "FAILED", "Endpoint not found")
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_detail = response.json().get('error', '')
                    if error_detail:
                        error_msg += f" - {error_detail}"
                except:
                    pass
                self.log_result("Price Broadcast API", "FAILED", error_msg)
                
        except Exception as e:
            self.log_result("Price Broadcast API", "FAILED", f"Error: {str(e)}")

    def test_notification_endpoints(self):
        """Test notification API endpoints"""
        print("\n🔔 Testing notification endpoints...")
        
        # Test user notification
        notification_data = {
            'title': 'Test Notification',
            'message': 'This is a test notification',
            'type': 'info'
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/api/socketio/notify-user/test_user",
                json=notification_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_result("User Notification API", "PASSED", "Notification sent successfully")
            elif response.status_code == 404:
                self.log_result("User Notification API", "FAILED", "Endpoint not found")
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_detail = response.json().get('error', '')
                    if error_detail:
                        error_msg += f" - {error_detail}"
                except:
                    pass
                self.log_result("User Notification API", "FAILED", error_msg)
                
        except Exception as e:
            self.log_result("User Notification API", "FAILED", f"Error: {str(e)}")

    def test_cart_update_endpoints(self):
        """Test cart update API endpoints"""
        print("\n🛒 Testing cart update endpoints...")
        
        cart_data = {
            'user_id': 'test_user',
            'session_id': 'test_session',
            'cart_data': {
                'items': [
                    {'product_id': 1, 'quantity': 2, 'price': 99.99}
                ],
                'total': 199.98
            }
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/api/socketio/events/cart-update",
                json=cart_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_result("Cart Update API", "PASSED", "Cart update sent successfully")
            elif response.status_code == 404:
                self.log_result("Cart Update API", "FAILED", "Endpoint not found")
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_detail = response.json().get('error', '')
                    if error_detail:
                        error_msg += f" - {error_detail}"
                except:
                    pass
                self.log_result("Cart Update API", "FAILED", error_msg)
                
        except Exception as e:
            self.log_result("Cart Update API", "FAILED", f"Error: {str(e)}")

    def test_socketio_manager_integration(self):
        """Test if SocketIO manager is properly integrated"""
        print("\n🔧 Testing SocketIO manager integration...")
        
        # Check if the Flask-SocketIO manager is working by testing a simple endpoint
        try:
            response = requests.get(f"{self.server_url}/api/health", timeout=10)
            
            if response.status_code == 200:
                # Check response headers for SocketIO indicators
                headers = response.headers
                
                # Look for CORS headers that might indicate SocketIO setup
                cors_headers = [h for h in headers.keys() if 'cors' in h.lower() or 'access-control' in h.lower()]
                
                if cors_headers:
                    self.log_result("SocketIO CORS Setup", "PASSED", f"CORS headers present: {cors_headers}")
                else:
                    self.log_result("SocketIO CORS Setup", "FAILED", "No CORS headers found")
                
                # Test if server supports WebSocket upgrade (basic check)
                self.log_result("SocketIO Manager Integration", "PASSED", "Server is responding normally")
            else:
                self.log_result("SocketIO Manager Integration", "FAILED", f"Server not responding properly: {response.status_code}")
                
        except Exception as e:
            self.log_result("SocketIO Manager Integration", "FAILED", f"Error: {str(e)}")

    def generate_report(self):
        """Generate test report"""
        print("\n" + "="*60)
        print("📊 SIMPLE SOCKETIO TEST REPORT")
        print("="*60)
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total Tests: {self.results['total']}")
        print(f"   Passed: {self.results['passed']} ({self.results['passed']/max(self.results['total'],1)*100:.1f}%)")
        print(f"   Failed: {self.results['failed']} ({self.results['failed']/max(self.results['total'],1)*100:.1f}%)")
        
        # Failed tests
        failed_tests = [r for r in self.results['details'] if r['status'] == 'FAILED']
        if failed_tests:
            print(f"\n❌ FAILED TESTS ({len(failed_tests)}):")
            for test in failed_tests:
                print(f"   {test['test_name']}: {test['message']}")
        
        # Passed tests
        passed_tests = [r for r in self.results['details'] if r['status'] == 'PASSED']
        if passed_tests:
            print(f"\n✅ PASSED TESTS ({len(passed_tests)}):")
            for test in passed_tests:
                print(f"   {test['test_name']}: {test['message']}")
        
        # Save detailed report
        report_file = f"simple_socketio_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if self.results['failed'] == 0:
            print("   🎉 All SocketIO tests passed! System is working well.")
        else:
            if any('not found' in test['message'] for test in failed_tests):
                print("   🔧 Some SocketIO API endpoints are not registered. Check if socketio_routes.py is properly imported in app.py")
            if any('Error:' in test['message'] for test in failed_tests):
                print("   🚨 Connection or server errors detected. Check server logs and ensure SocketIO is properly configured.")

    def run_all_tests(self):
        """Run all simple SocketIO tests"""
        print("🚀 Starting Simple SocketIO Testing...")
        print(f"🎯 Target: {self.server_url}")
        
        start_time = time.time()
        
        # Run test suites
        self.test_socketio_page_access()
        self.test_socketio_api_endpoints()
        self.test_broadcast_endpoints()
        self.test_notification_endpoints()
        self.test_cart_update_endpoints()
        self.test_socketio_manager_integration()
        
        total_time = time.time() - start_time
        print(f"\n⏱️  Total testing time: {total_time:.2f} seconds")
        
        # Generate report
        self.generate_report()
        
        return self.results

if __name__ == "__main__":
    tester = SimpleSocketIOTester()
    tester.run_all_tests()
