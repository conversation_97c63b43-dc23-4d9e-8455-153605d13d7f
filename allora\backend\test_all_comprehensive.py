#!/usr/bin/env python3
"""
Unified Comprehensive Testing Script
===================================

Runs both API endpoint tests and SocketIO tests, then generates a unified report.
This is the main test runner for the Allora backend application.

Usage:
    python test_all_comprehensive.py

Requirements:
    - Backend server running on http://localhost:5000
    - Redis server running (optional, for enhanced features)
    - MySQL database accessible
"""

import sys
import os
import time
import json
import subprocess
from datetime import datetime
from typing import Dict, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from test_api_endpoints_comprehensive import APIEndpointTester
    from test_socketio_comprehensive import SocketIOTester
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure both test_api_endpoints_comprehensive.py and test_socketio_comprehensive.py are in the same directory")
    sys.exit(1)

class UnifiedTester:
    def __init__(self, server_url: str = "http://localhost:5000"):
        self.server_url = server_url
        self.results = {
            'api_results': {},
            'socketio_results': {},
            'unified_summary': {},
            'recommendations': [],
            'test_environment': {},
            'timestamp': datetime.now().isoformat()
        }
        
    def check_server_health(self) -> bool:
        """Check if the server is running and healthy"""
        print("🏥 Checking server health...")
        
        try:
            import requests
            response = requests.get(f"{self.server_url}/api/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Server is healthy: {health_data.get('message', 'OK')}")
                
                # Store environment info
                self.results['test_environment'] = {
                    'server_url': self.server_url,
                    'server_status': 'healthy',
                    'server_response': health_data,
                    'test_start_time': datetime.now().isoformat()
                }
                return True
            else:
                print(f"⚠️  Server health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Server health check error: {e}")
            print("Make sure the backend server is running on the specified URL")
            return False

    def check_dependencies(self) -> Dict[str, bool]:
        """Check if required dependencies are available"""
        print("🔍 Checking dependencies...")
        
        dependencies = {
            'requests': False,
            'socketio': False,
            'redis': False,
            'mysql': False
        }
        
        # Check requests
        try:
            import requests
            dependencies['requests'] = True
            print("✅ requests library available")
        except ImportError:
            print("❌ requests library not available")
        
        # Check socketio
        try:
            import socketio
            dependencies['socketio'] = True
            print("✅ python-socketio library available")
        except ImportError:
            print("❌ python-socketio library not available")
        
        # Check Redis connection
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            dependencies['redis'] = True
            print("✅ Redis server accessible")
        except Exception:
            print("⚠️  Redis server not accessible (optional)")
        
        # Check MySQL connection via server
        try:
            import requests
            response = requests.get(f"{self.server_url}/api/health/database", timeout=10)
            if response.status_code == 200:
                dependencies['mysql'] = True
                print("✅ Database connection healthy")
            else:
                print("⚠️  Database connection issues")
        except Exception:
            print("⚠️  Could not verify database connection")
        
        self.results['test_environment']['dependencies'] = dependencies
        return dependencies

    def run_api_tests(self) -> Dict[str, Any]:
        """Run API endpoint tests"""
        print("\n" + "="*60)
        print("🌐 STARTING API ENDPOINT TESTS")
        print("="*60)
        
        try:
            api_tester = APIEndpointTester(self.server_url)
            api_tester.run_all_tests()
            
            self.results['api_results'] = api_tester.results
            return api_tester.results
            
        except Exception as e:
            print(f"❌ API tests failed: {e}")
            self.results['api_results'] = {
                'error': str(e),
                'passed': 0,
                'failed': 1,
                'total': 1
            }
            return self.results['api_results']

    def run_socketio_tests(self) -> Dict[str, Any]:
        """Run SocketIO tests"""
        print("\n" + "="*60)
        print("🔌 STARTING SOCKETIO TESTS")
        print("="*60)
        
        try:
            socketio_tester = SocketIOTester(self.server_url)
            socketio_tester.run_all_tests()
            
            self.results['socketio_results'] = socketio_tester.results
            return socketio_tester.results
            
        except Exception as e:
            print(f"❌ SocketIO tests failed: {e}")
            self.results['socketio_results'] = {
                'error': str(e),
                'passed': 0,
                'failed': 1,
                'total': 1
            }
            return self.results['socketio_results']

    def analyze_results(self):
        """Analyze test results and generate recommendations"""
        print("\n🔍 Analyzing test results...")
        
        api_results = self.results.get('api_results', {})
        socketio_results = self.results.get('socketio_results', {})
        
        # Calculate unified summary
        total_tests = api_results.get('total', 0) + socketio_results.get('total', 0)
        total_passed = api_results.get('passed', 0) + socketio_results.get('passed', 0)
        total_failed = api_results.get('failed', 0) + socketio_results.get('failed', 0)
        
        success_rate = (total_passed / max(total_tests, 1)) * 100
        
        self.results['unified_summary'] = {
            'total_tests': total_tests,
            'total_passed': total_passed,
            'total_failed': total_failed,
            'success_rate': success_rate,
            'api_success_rate': (api_results.get('passed', 0) / max(api_results.get('total', 1), 1)) * 100,
            'socketio_success_rate': (socketio_results.get('passed', 0) / max(socketio_results.get('total', 1), 1)) * 100
        }
        
        # Generate recommendations
        recommendations = []
        
        if success_rate < 80:
            recommendations.append("🚨 CRITICAL: Overall success rate is below 80%. Immediate attention required.")
        elif success_rate < 90:
            recommendations.append("⚠️  WARNING: Success rate is below 90%. Consider investigating failures.")
        else:
            recommendations.append("✅ GOOD: High success rate indicates stable system.")
        
        # API-specific recommendations
        api_success = self.results['unified_summary']['api_success_rate']
        if api_success < 80:
            recommendations.append("🔴 API endpoints have critical issues. Check authentication, database connections, and error handling.")
        elif api_success < 90:
            recommendations.append("🟡 Some API endpoints are failing. Review error logs and fix failing routes.")
        
        # SocketIO-specific recommendations
        socketio_success = self.results['unified_summary']['socketio_success_rate']
        if socketio_success < 80:
            recommendations.append("🔴 SocketIO functionality has critical issues. Check WebSocket connections and event handlers.")
        elif socketio_success < 90:
            recommendations.append("🟡 Some SocketIO features are failing. Review real-time event broadcasting.")
        
        # Performance recommendations
        api_performance = api_results.get('performance', {})
        if api_performance:
            slow_endpoints = []
            for endpoint, times in api_performance.items():
                if times and max(times) > 5.0:  # Slower than 5 seconds
                    slow_endpoints.append(endpoint)
            
            if slow_endpoints:
                recommendations.append(f"⚡ Performance issue: These endpoints are slow: {', '.join(slow_endpoints[:3])}")
        
        # Dependency recommendations
        deps = self.results['test_environment'].get('dependencies', {})
        if not deps.get('redis', False):
            recommendations.append("💡 Consider enabling Redis for better caching and session management.")
        
        self.results['recommendations'] = recommendations

    def generate_unified_report(self):
        """Generate comprehensive unified report"""
        print("\n" + "="*80)
        print("📊 UNIFIED COMPREHENSIVE TEST REPORT")
        print("="*80)
        
        # Environment summary
        print(f"\n🌍 TEST ENVIRONMENT:")
        print(f"   Server URL: {self.server_url}")
        print(f"   Test Time: {self.results['timestamp']}")
        
        deps = self.results['test_environment'].get('dependencies', {})
        print(f"   Dependencies: requests={deps.get('requests', False)}, "
              f"socketio={deps.get('socketio', False)}, "
              f"redis={deps.get('redis', False)}, "
              f"mysql={deps.get('mysql', False)}")
        
        # Unified summary
        summary = self.results['unified_summary']
        print(f"\n📈 OVERALL SUMMARY:")
        print(f"   Total Tests: {summary['total_tests']}")
        print(f"   Passed: {summary['total_passed']} ({summary['success_rate']:.1f}%)")
        print(f"   Failed: {summary['total_failed']}")
        
        # Individual test suite results
        print(f"\n🌐 API ENDPOINT RESULTS:")
        api_results = self.results['api_results']
        print(f"   Tests: {api_results.get('total', 0)}")
        print(f"   Success Rate: {summary['api_success_rate']:.1f}%")
        
        print(f"\n🔌 SOCKETIO RESULTS:")
        socketio_results = self.results['socketio_results']
        print(f"   Tests: {socketio_results.get('total', 0)}")
        print(f"   Success Rate: {summary['socketio_success_rate']:.1f}%")
        
        # Recommendations
        if self.results['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(self.results['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        # Performance insights
        api_performance = api_results.get('performance', {})
        if api_performance:
            print(f"\n⚡ PERFORMANCE INSIGHTS:")
            for endpoint, times in list(api_performance.items())[:5]:  # Show top 5
                if times:
                    avg_time = sum(times) / len(times)
                    print(f"   {endpoint}: {avg_time:.3f}s avg")
        
        # Save unified report
        report_file = f"unified_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Unified report saved to: {report_file}")
        
        # Final status
        if summary['success_rate'] >= 90:
            print(f"\n🎉 EXCELLENT: System is performing well with {summary['success_rate']:.1f}% success rate!")
        elif summary['success_rate'] >= 80:
            print(f"\n👍 GOOD: System is mostly stable with {summary['success_rate']:.1f}% success rate.")
        else:
            print(f"\n⚠️  NEEDS ATTENTION: System has issues with {summary['success_rate']:.1f}% success rate.")

    def run_all_tests(self):
        """Run all comprehensive tests"""
        print("🚀 STARTING UNIFIED COMPREHENSIVE TESTING")
        print(f"🎯 Target Server: {self.server_url}")
        print("="*80)
        
        start_time = time.time()
        
        # Pre-flight checks
        if not self.check_server_health():
            print("❌ Server health check failed. Aborting tests.")
            return False
        
        self.check_dependencies()
        
        # Run test suites
        self.run_api_tests()
        self.run_socketio_tests()
        
        # Analyze and report
        self.analyze_results()
        
        total_time = time.time() - start_time
        print(f"\n⏱️  Total testing time: {total_time:.2f} seconds")
        
        self.generate_unified_report()
        
        return True

if __name__ == "__main__":
    # Allow custom server URL via command line
    server_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:5000"
    
    tester = UnifiedTester(server_url)
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
