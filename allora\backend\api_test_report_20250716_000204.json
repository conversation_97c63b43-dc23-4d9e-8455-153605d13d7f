{"passed": 36, "failed": 22, "skipped": 0, "total": 58, "details": [{"endpoint": "/", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.003990650177001953, "timestamp": "2025-07-16T00:01:56.173437"}, {"endpoint": "/socketio-test", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.005986690521240234, "timestamp": "2025-07-16T00:01:56.283441"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.004196882247924805, "timestamp": "2025-07-16T00:01:56.392734"}, {"endpoint": "/api/health/status", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 1.0209949016571045, "timestamp": "2025-07-16T00:01:57.517591"}, {"endpoint": "/api/health/database", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.009244203567504883, "timestamp": "2025-07-16T00:01:57.629341"}, {"endpoint": "/api/categories", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.012135744094848633, "timestamp": "2025-07-16T00:01:57.755801"}, {"endpoint": "/api/products", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.007794857025146484, "timestamp": "2025-07-16T00:01:57.877586"}, {"endpoint": "/api/sellers", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.03312087059020996, "timestamp": "2025-07-16T00:01:58.025426"}, {"endpoint": "/api/search/suggestions", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.002994537353515625, "timestamp": "2025-07-16T00:01:58.135731"}, {"endpoint": "/api/search/filters", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.027225732803344727, "timestamp": "2025-07-16T00:01:58.268501"}, {"endpoint": "/api/community_insights", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.02104473114013672, "timestamp": "2025-07-16T00:01:58.402873"}, {"endpoint": "/api/products/best-sellers", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.010296106338500977, "timestamp": "2025-07-16T00:01:58.516057"}, {"endpoint": "/api/products/new-arrivals", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.020944833755493164, "timestamp": "2025-07-16T00:01:58.651694"}, {"endpoint": "/api/inventory_predictions", "method": "GET", "status": "FAILED", "response_code": 500, "message": "Expected [200, 201, 202], got 500 - Failed to fetch inventory predictions: [Errno 2] No such file or directory: 'C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\backend\\\\models\\\\InventoryPredictionModel\\\\inventory_model.pkl'", "response_time": 0.004915714263916016, "timestamp": "2025-07-16T00:01:58.761127"}, {"endpoint": "/api/price_trends", "method": "GET", "status": "FAILED", "response_code": 500, "message": "Expected [200, 201, 202], got 500 - Failed to fetch price trend predictions: [Errno 2] No such file or directory: 'C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\backend\\\\models\\\\PriceTrendsModel\\\\price_trends_model.pkl'", "response_time": 0.002912282943725586, "timestamp": "2025-07-16T00:01:58.868319"}, {"endpoint": "/api/advanced_inventory_predictions", "method": "GET", "status": "FAILED", "response_code": 500, "message": "Expected [200, 201, 202], got 500 - Failed to fetch advanced inventory predictions: [Errno 2] No such file or directory: 'C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\backend\\\\models\\\\InventoryPredictionModel\\\\inventory_model.pkl'", "response_time": 0.004981040954589844, "timestamp": "2025-07-16T00:01:58.979958"}, {"endpoint": "/api/advanced_price_trends", "method": "GET", "status": "FAILED", "response_code": 500, "message": "Expected [200, 201, 202], got 500 - Failed to fetch advanced price trend predictions: [Errno 2] No such file or directory: 'C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\backend\\\\models\\\\PriceTrendsModel\\\\price_trends_model.pkl'", "response_time": 0.003955841064453125, "timestamp": "2025-07-16T00:01:59.084797"}, {"endpoint": "/sitemap.xml", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.012067794799804688, "timestamp": "2025-07-16T00:01:59.201524"}, {"endpoint": "/robots.txt", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.003330707550048828, "timestamp": "2025-07-16T00:01:59.316505"}, {"endpoint": "/api/content", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.024900197982788086, "timestamp": "2025-07-16T00:01:59.446228"}, {"endpoint": "/api/cache/test", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.006553173065185547, "timestamp": "2025-07-16T00:01:59.554358"}, {"endpoint": "/api/session/test", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.17049026489257812, "timestamp": "2025-07-16T00:01:59.827579"}, {"endpoint": "/api/test/sentry/status", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.004959821701049805, "timestamp": "2025-07-16T00:01:59.944858"}, {"endpoint": "/api/oauth/providers", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.004624843597412109, "timestamp": "2025-07-16T00:02:00.054629"}, {"endpoint": "/api/signup", "method": "POST", "status": "FAILED", "response_code": 429, "message": "Expected [400, 422], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3600, 'status': 429}", "response_time": 0.0049839019775390625, "timestamp": "2025-07-16T00:02:00.059613"}, {"endpoint": "/api/login", "method": "POST", "status": "FAILED", "response_code": 429, "message": "Expected [401, 400], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3599, 'status': 429}", "response_time": 0.003991842269897461, "timestamp": "2025-07-16T00:02:00.063605"}, {"endpoint": "/api/products?page=1&per_page=10", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3599, 'status': 429}", "response_time": 0.00484013557434082, "timestamp": "2025-07-16T00:02:00.069586"}, {"endpoint": "/api/products?category=Electronics", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3599, 'status': 429}", "response_time": 0.003995656967163086, "timestamp": "2025-07-16T00:02:00.073582"}, {"endpoint": "/api/products?sort=price_asc", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3599, 'status': 429}", "response_time": 0.007092475891113281, "timestamp": "2025-07-16T00:02:00.080674"}, {"endpoint": "/api/products?min_price=100&max_price=1000", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3599, 'status': 429}", "response_time": 0.00487065315246582, "timestamp": "2025-07-16T00:02:00.085545"}, {"endpoint": "/api/categories/Electronics/products", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.03045177459716797, "timestamp": "2025-07-16T00:02:00.115997"}, {"endpoint": "/api/products/1", "method": "GET", "status": "FAILED", "response_code": 404, "message": "Expected [200, 201, 202], got 404 - Product not found: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.", "response_time": 0.013928413391113281, "timestamp": "2025-07-16T00:02:00.129925"}, {"endpoint": "/api/products/batch", "method": "POST", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3599, 'status': 429}", "response_time": 0.003009319305419922, "timestamp": "2025-07-16T00:02:00.132935"}, {"endpoint": "/api/search?q=laptop", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 1.8679125308990479, "timestamp": "2025-07-16T00:02:02.001811"}, {"endpoint": "/api/search/autocomplete?q=lap", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.07779073715209961, "timestamp": "2025-07-16T00:02:02.079602"}, {"endpoint": "/api/search/suggestions?q=phone", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.009972333908081055, "timestamp": "2025-07-16T00:02:02.089574"}, {"endpoint": "/api/search/similar/1", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.0409238338470459, "timestamp": "2025-07-16T00:02:02.130498"}, {"endpoint": "/api/search/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.07073354721069336, "timestamp": "2025-07-16T00:02:02.202196"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.01000833511352539, "timestamp": "2025-07-16T00:02:02.214165"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 0.016955852508544922, "timestamp": "2025-07-16T00:02:02.226100"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.0603532791137695, "timestamp": "2025-07-16T00:02:04.266561"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.055368423461914, "timestamp": "2025-07-16T00:02:04.269533"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.0573973655700684, "timestamp": "2025-07-16T00:02:04.272525"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.056142568588257, "timestamp": "2025-07-16T00:02:04.275518"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.0633833408355713, "timestamp": "2025-07-16T00:02:04.285491"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.074352741241455, "timestamp": "2025-07-16T00:02:04.289481"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.0863189697265625, "timestamp": "2025-07-16T00:02:04.291475"}, {"endpoint": "/api/health", "method": "GET", "status": "PASSED", "response_code": 200, "message": "Success", "response_time": 2.088350772857666, "timestamp": "2025-07-16T00:02:04.299488"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.00498509407043457, "timestamp": "2025-07-16T00:02:04.306435"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.0069844722747802734, "timestamp": "2025-07-16T00:02:04.310427"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.012965679168701172, "timestamp": "2025-07-16T00:02:04.319400"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.015916109085083008, "timestamp": "2025-07-16T00:02:04.321397"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.01598954200744629, "timestamp": "2025-07-16T00:02:04.324421"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.025968074798583984, "timestamp": "2025-07-16T00:02:04.334399"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.026901721954345703, "timestamp": "2025-07-16T00:02:04.338359"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.0199434757232666, "timestamp": "2025-07-16T00:02:04.340374"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.025933504104614258, "timestamp": "2025-07-16T00:02:04.345334"}, {"endpoint": "/api/products", "method": "GET", "status": "FAILED", "response_code": 429, "message": "Expected [200, 201, 202], got 429 - {'code': 'RATE_LIMIT_EXCEEDED', 'message': 'Rate limit exceeded. Too many requests.', 'retry_after': 3595, 'status': 429}", "response_time": 0.024962902069091797, "timestamp": "2025-07-16T00:02:04.348353"}], "performance": {"/": [0.003990650177001953], "/socketio-test": [0.005986690521240234], "/api/health": [0.004196882247924805, 0.01000833511352539, 0.016955852508544922, 2.0603532791137695, 2.055368423461914, 2.0573973655700684, 2.056142568588257, 2.0633833408355713, 2.074352741241455, 2.0863189697265625, 2.088350772857666], "/api/health/status": [1.0209949016571045], "/api/health/database": [0.009244203567504883], "/api/categories": [0.012135744094848633], "/api/products": [0.007794857025146484, 0.00498509407043457, 0.0069844722747802734, 0.012965679168701172, 0.015916109085083008, 0.01598954200744629, 0.025968074798583984, 0.026901721954345703, 0.0199434757232666, 0.025933504104614258, 0.024962902069091797], "/api/sellers": [0.03312087059020996], "/api/search/suggestions": [0.002994537353515625], "/api/search/filters": [0.027225732803344727], "/api/community_insights": [0.02104473114013672], "/api/products/best-sellers": [0.010296106338500977], "/api/products/new-arrivals": [0.020944833755493164], "/api/inventory_predictions": [0.004915714263916016], "/api/price_trends": [0.002912282943725586], "/api/advanced_inventory_predictions": [0.004981040954589844], "/api/advanced_price_trends": [0.003955841064453125], "/sitemap.xml": [0.012067794799804688], "/robots.txt": [0.003330707550048828], "/api/content": [0.024900197982788086], "/api/cache/test": [0.006553173065185547], "/api/session/test": [0.17049026489257812], "/api/test/sentry/status": [0.004959821701049805], "/api/oauth/providers": [0.004624843597412109], "/api/signup": [0.0049839019775390625], "/api/login": [0.003991842269897461], "/api/products?page=1&per_page=10": [0.00484013557434082], "/api/products?category=Electronics": [0.003995656967163086], "/api/products?sort=price_asc": [0.007092475891113281], "/api/products?min_price=100&max_price=1000": [0.00487065315246582], "/api/categories/Electronics/products": [0.03045177459716797], "/api/products/1": [0.013928413391113281], "/api/products/batch": [0.003009319305419922], "/api/search?q=laptop": [1.8679125308990479], "/api/search/autocomplete?q=lap": [0.07779073715209961], "/api/search/suggestions?q=phone": [0.009972333908081055], "/api/search/similar/1": [0.0409238338470459], "/api/search/health": [0.07073354721069336]}, "errors": []}