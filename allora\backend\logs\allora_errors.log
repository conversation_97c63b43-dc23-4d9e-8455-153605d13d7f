2025-07-13 10:16:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:20:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:30:27 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:33:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:34:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:41:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:49:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:56:34 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:00:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:03:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:07:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:10:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:28:33 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:29:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:31:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:35:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:38:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:41:56 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:44:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:46:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:46:38 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:50:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:51:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:18:24 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:20:00 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:22:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:28:38 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:35:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:40:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:44:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:45:18 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:46:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:49:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:55:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 13:06:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:17:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:17:10 - allora - ERROR - Failed to blacklist token: value is not an integer or out of range
2025-07-13 14:30:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:31:42 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:47:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:12:48 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:21:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:22:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:22:37 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:23:27 - webhook_handlers - ERROR - Error parsing timestamp 2025-07-13T10:00:00Z: time data '2025-07-13T10:00:00Z' does not match format '%Y-%m-%d %H:%M:%S'
2025-07-13 15:31:39 - webhook_handlers - ERROR - Error parsing timestamp 2025-07-13T10:00:00Z: time data '2025-07-13T10:00:00Z' does not match format '%Y-%m-%d %H:%M:%S'
2025-07-13 16:13:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:44:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:47:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:52:28 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:54:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:02:36 - engineio.server - ERROR - The client is using an unsupported version of the Socket.IO or Engine.IO protocols (further occurrences of this error will be logged with level INFO)
2025-07-13 17:08:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:10:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:13:07 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:16:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:18:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:19:18 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:04:22 - allora - ERROR - Unhandled exception: MethodNotAllowed at None
2025-07-13 18:04:22 - allora - ERROR - Unhandled exception: 405 Method Not Allowed: The method is not allowed for the requested URL.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1458, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1440, in raise_routing_exception
    raise request.routing_exception  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025-07-13 18:04:37 - allora - ERROR - Unhandled exception: MethodNotAllowed at None
2025-07-13 18:04:37 - allora - ERROR - Unhandled exception: 405 Method Not Allowed: The method is not allowed for the requested URL.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1458, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1440, in raise_routing_exception
    raise request.routing_exception  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025-07-13 18:15:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting basic metrics: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting shipment list: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting basic metrics: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:23:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:24:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:56:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:13:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:15:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:43:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:44:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:52:12 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:24 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:24 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:25:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:27:19 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:30:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:33:52 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:42:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:42:47 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 20:45:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting basic metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting shipment list: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 21:05:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:05:10 - fulfillment_api - ERROR - No tracking number in webhook from blue_dart
2025-07-13 21:06:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:07:50 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 21:16:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:21:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:30:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:30:03 - allora - ERROR - Test error message
2025-07-13 21:30:03 - __main__ - ERROR - PERFORMANCE: failing_function failed after 0.000s - Test exception
2025-07-13 21:37:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:42:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:49:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:49:46 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:49:46 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:49:46 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:57:11 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:11 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:11 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:30 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:58:21 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:59:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:59:52 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 21:59:52 - sustainability_api - ERROR - Error getting green heroes: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 21:59:52 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:04:02 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:02 - sustainability_api - ERROR - Error getting green heroes: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:02 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:25 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:05:55 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:09:15 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:09:32 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:09:44 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:10:13 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:13:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:19:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:24:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:25:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:31:01 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:31:02 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:33:30 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:33:30 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:36:12 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:36:12 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:37:31 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:37:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:42:14 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:42:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:02:03 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 18:02:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:10:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:59:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:59:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 19:01:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 19:43:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 20:11:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:00:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:01:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:03:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:47:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 23:15:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
