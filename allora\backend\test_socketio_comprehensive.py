#!/usr/bin/env python3
"""
Comprehensive SocketIO Testing Script
====================================

Tests all SocketIO events and real-time functionality in the Allora backend.
Includes connection testing, event broadcasting, authentication, and performance testing.

Usage:
    python test_socketio_comprehensive.py

Requirements:
    - Backend server running on http://localhost:5000
    - Flask-SocketIO enabled
    - Redis server running (optional, for pub/sub)
"""

import socketio
import requests
import json
import time
import threading
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor

class SocketIOTester:
    def __init__(self, server_url: str = "http://localhost:5000"):
        self.server_url = server_url
        self.results = {
            'passed': 0,
            'failed': 0,
            'total': 0,
            'details': [],
            'events_received': [],
            'performance': {},
            'connections': []
        }
        self.clients = []
        self.received_events = []
        self.auth_token = None
        
    def log_result(self, test_name: str, status: str, message: str = "", 
                   response_time: float = None):
        """Log test result"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'response_time': response_time,
            'timestamp': datetime.now().isoformat()
        }
        self.results['details'].append(result)
        self.results['total'] += 1
        
        if status == 'PASSED':
            self.results['passed'] += 1
        else:
            self.results['failed'] += 1
            
        print(f"{'✅' if status == 'PASSED' else '❌'} {test_name}: {message}")

    def setup_authentication(self):
        """Setup authentication for testing"""
        print("🔐 Setting up authentication...")
        
        try:
            # Login to get auth token
            login_data = {
                'email': '<EMAIL>',
                'password': 'TestPassword123!'
            }
            
            response = requests.post(f"{self.server_url}/api/login", json=login_data, timeout=10)
            if response.status_code == 200:
                self.auth_token = response.json().get('access_token')
                print("✅ Authentication successful")
                return True
            else:
                print("⚠️  Authentication failed")
                return False
                
        except Exception as e:
            print(f"⚠️  Authentication error: {e}")
            return False

    def create_client(self, client_id: str = None, auth: dict = None) -> socketio.SimpleClient:
        """Create a SocketIO client"""
        client = socketio.SimpleClient()

        # Store client reference for event handling
        client_ref = {'client_id': client_id, 'tester': self}

        def on_connect():
            self.log_result(f"Client {client_id} Connect", "PASSED", "Connected successfully")

        def on_disconnect():
            self.log_result(f"Client {client_id} Disconnect", "PASSED", "Disconnected successfully")

        def on_inventory_update(data):
            self.received_events.append(('inventory_update', data, datetime.now()))
            self.log_result("Inventory Update Event", "PASSED", f"Received: {data}")

        def on_price_update(data):
            self.received_events.append(('price_update', data, datetime.now()))
            self.log_result("Price Update Event", "PASSED", f"Received: {data}")

        def on_cart_update(data):
            self.received_events.append(('cart_update', data, datetime.now()))
            self.log_result("Cart Update Event", "PASSED", f"Received: {data}")

        def on_order_update(data):
            self.received_events.append(('order_update', data, datetime.now()))
            self.log_result("Order Update Event", "PASSED", f"Received: {data}")

        def on_notification(data):
            self.received_events.append(('notification', data, datetime.now()))
            self.log_result("Notification Event", "PASSED", f"Received: {data}")

        def on_admin_notification(data):
            self.received_events.append(('admin_notification', data, datetime.now()))
            self.log_result("Admin Notification Event", "PASSED", f"Received: {data}")

        def on_pong(data):
            self.received_events.append(('pong', data, datetime.now()))

        def on_heartbeat_ack(data):
            self.received_events.append(('heartbeat_ack', data, datetime.now()))

        def on_subscribed(data):
            self.received_events.append(('subscribed', data, datetime.now()))
            self.log_result("Subscription Event", "PASSED", f"Subscribed to: {data.get('events', [])}")

        # Register event handlers manually
        client.register_namespace(socketio.SimpleClientNamespace())

        return client

    def test_basic_connection(self):
        """Test basic SocketIO connection"""
        print("\n🔌 Testing basic SocketIO connection...")
        
        client = self.create_client("basic")
        
        try:
            start_time = time.time()
            client.connect(self.server_url)
            connection_time = time.time() - start_time
            
            self.log_result("Basic Connection", "PASSED", 
                          f"Connected in {connection_time:.3f}s", connection_time)
            
            # Test if connected
            if client.connected:
                self.log_result("Connection Status", "PASSED", "Client is connected")
            else:
                self.log_result("Connection Status", "FAILED", "Client not connected")
            
            client.disconnect()
            self.clients.append(client)
            
        except Exception as e:
            self.log_result("Basic Connection", "FAILED", f"Connection failed: {str(e)}")

    def test_authenticated_connection(self):
        """Test authenticated SocketIO connection"""
        print("\n🔐 Testing authenticated SocketIO connection...")
        
        if not self.auth_token:
            self.log_result("Authenticated Connection", "FAILED", "No auth token available")
            return
        
        client = self.create_client("authenticated")
        
        try:
            auth_data = {
                'user_id': 'test_user',
                'token': self.auth_token,
                'is_admin': False
            }
            
            start_time = time.time()
            client.connect(self.server_url, auth=auth_data)
            connection_time = time.time() - start_time
            
            self.log_result("Authenticated Connection", "PASSED", 
                          f"Connected with auth in {connection_time:.3f}s", connection_time)
            
            client.disconnect()
            
        except Exception as e:
            self.log_result("Authenticated Connection", "FAILED", f"Auth connection failed: {str(e)}")

    def test_ping_pong(self):
        """Test ping/pong functionality"""
        print("\n🏓 Testing ping/pong...")
        
        client = self.create_client("ping_test")
        
        try:
            client.connect(self.server_url)
            
            # Send ping
            start_time = time.time()
            client.emit('ping')
            
            # Wait for pong
            time.sleep(1)
            
            # Check if pong was received
            pong_events = [e for e in self.received_events if e[0] == 'pong']
            if pong_events:
                response_time = time.time() - start_time
                self.log_result("Ping/Pong", "PASSED", 
                              f"Pong received in {response_time:.3f}s", response_time)
            else:
                self.log_result("Ping/Pong", "FAILED", "No pong response received")
            
            client.disconnect()
            
        except Exception as e:
            self.log_result("Ping/Pong", "FAILED", f"Ping/pong test failed: {str(e)}")

    def test_subscription_events(self):
        """Test event subscription functionality"""
        print("\n📡 Testing event subscriptions...")
        
        client = self.create_client("subscription_test")
        
        try:
            client.connect(self.server_url)
            
            # Subscribe to events
            subscription_data = {
                'events': ['inventory', 'prices', 'orders']
            }
            
            client.emit('subscribe', subscription_data)
            time.sleep(1)
            
            # Check if subscription confirmation was received
            subscribed_events = [e for e in self.received_events if e[0] == 'subscribed']
            if subscribed_events:
                self.log_result("Event Subscription", "PASSED", 
                              f"Subscribed to events: {subscribed_events[-1][1].get('events', [])}")
            else:
                self.log_result("Event Subscription", "FAILED", "No subscription confirmation")
            
            client.disconnect()
            
        except Exception as e:
            self.log_result("Event Subscription", "FAILED", f"Subscription test failed: {str(e)}")

    def test_heartbeat(self):
        """Test heartbeat functionality"""
        print("\n💓 Testing heartbeat...")
        
        client = self.create_client("heartbeat_test")
        
        try:
            client.connect(self.server_url)
            
            # Send heartbeat
            start_time = time.time()
            client.emit('heartbeat')
            
            # Wait for heartbeat acknowledgment
            time.sleep(1)
            
            # Check if heartbeat_ack was received
            heartbeat_events = [e for e in self.received_events if e[0] == 'heartbeat_ack']
            if heartbeat_events:
                response_time = time.time() - start_time
                self.log_result("Heartbeat", "PASSED", 
                              f"Heartbeat ack received in {response_time:.3f}s", response_time)
            else:
                self.log_result("Heartbeat", "FAILED", "No heartbeat acknowledgment received")
            
            client.disconnect()
            
        except Exception as e:
            self.log_result("Heartbeat", "FAILED", f"Heartbeat test failed: {str(e)}")

    def test_broadcast_events(self):
        """Test broadcast event functionality using API endpoints"""
        print("\n📢 Testing broadcast events via API...")
        
        # Create a client to receive broadcasts
        client = self.create_client("broadcast_receiver")
        
        try:
            client.connect(self.server_url)
            time.sleep(1)  # Allow connection to establish
            
            # Test inventory update broadcast
            inventory_data = {
                'product_id': 1,
                'new_quantity': 50,
                'old_quantity': 100
            }
            
            response = requests.post(
                f"{self.server_url}/api/socketio/events/inventory-update",
                json=inventory_data,
                timeout=10
            )
            
            if response.status_code == 200:
                time.sleep(2)  # Wait for broadcast
                inventory_events = [e for e in self.received_events if e[0] == 'inventory_update']
                if inventory_events:
                    self.log_result("Inventory Broadcast", "PASSED", "Inventory update received")
                else:
                    self.log_result("Inventory Broadcast", "FAILED", "No inventory update received")
            else:
                self.log_result("Inventory Broadcast", "FAILED", f"API call failed: {response.status_code}")
            
            # Test price update broadcast
            price_data = {
                'product_id': 1,
                'new_price': 299.99,
                'old_price': 399.99
            }
            
            response = requests.post(
                f"{self.server_url}/api/socketio/events/price-update",
                json=price_data,
                timeout=10
            )
            
            if response.status_code == 200:
                time.sleep(2)  # Wait for broadcast
                price_events = [e for e in self.received_events if e[0] == 'price_update']
                if price_events:
                    self.log_result("Price Broadcast", "PASSED", "Price update received")
                else:
                    self.log_result("Price Broadcast", "FAILED", "No price update received")
            else:
                self.log_result("Price Broadcast", "FAILED", f"API call failed: {response.status_code}")
            
            client.disconnect()
            
        except Exception as e:
            self.log_result("Broadcast Events", "FAILED", f"Broadcast test failed: {str(e)}")

    def test_multiple_connections(self):
        """Test multiple simultaneous connections"""
        print("\n👥 Testing multiple connections...")
        
        num_clients = 5
        clients = []
        
        try:
            # Create multiple clients
            for i in range(num_clients):
                client = self.create_client(f"multi_{i}")
                clients.append(client)
            
            # Connect all clients
            start_time = time.time()
            for client in clients:
                client.connect(self.server_url)
            
            connection_time = time.time() - start_time
            
            # Check if all connected
            connected_count = sum(1 for client in clients if client.connected)
            
            if connected_count == num_clients:
                self.log_result("Multiple Connections", "PASSED", 
                              f"All {num_clients} clients connected in {connection_time:.3f}s")
            else:
                self.log_result("Multiple Connections", "FAILED", 
                              f"Only {connected_count}/{num_clients} clients connected")
            
            # Disconnect all clients
            for client in clients:
                if client.connected:
                    client.disconnect()
            
        except Exception as e:
            self.log_result("Multiple Connections", "FAILED", f"Multiple connection test failed: {str(e)}")

    def test_socketio_api_endpoints(self):
        """Test SocketIO-related API endpoints"""
        print("\n🔗 Testing SocketIO API endpoints...")
        
        api_endpoints = [
            ('/api/socketio/connections', 'GET'),
            ('/api/socketio/broadcast', 'POST', {'message': 'Test broadcast'}),
        ]
        
        for endpoint_info in api_endpoints:
            endpoint = endpoint_info[0]
            method = endpoint_info[1]
            data = endpoint_info[2] if len(endpoint_info) > 2 else None
            
            try:
                url = f"{self.server_url}{endpoint}"
                
                if method == 'GET':
                    response = requests.get(url, timeout=10)
                else:
                    response = requests.post(url, json=data, timeout=10)
                
                if response.status_code in [200, 201]:
                    self.log_result(f"API {method} {endpoint}", "PASSED", 
                                  f"Status: {response.status_code}")
                else:
                    self.log_result(f"API {method} {endpoint}", "FAILED", 
                                  f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"API {method} {endpoint}", "FAILED", f"Error: {str(e)}")

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📊 COMPREHENSIVE SOCKETIO TEST REPORT")
        print("="*60)
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total Tests: {self.results['total']}")
        print(f"   Passed: {self.results['passed']} ({self.results['passed']/max(self.results['total'],1)*100:.1f}%)")
        print(f"   Failed: {self.results['failed']} ({self.results['failed']/max(self.results['total'],1)*100:.1f}%)")
        
        # Events received summary
        if self.received_events:
            print(f"\n📡 EVENTS RECEIVED ({len(self.received_events)}):")
            event_counts = {}
            for event_name, _, _ in self.received_events:
                event_counts[event_name] = event_counts.get(event_name, 0) + 1
            
            for event_name, count in event_counts.items():
                print(f"   {event_name}: {count}")
        
        # Failed tests
        failed_tests = [r for r in self.results['details'] if r['status'] == 'FAILED']
        if failed_tests:
            print(f"\n❌ FAILED TESTS ({len(failed_tests)}):")
            for test in failed_tests:
                print(f"   {test['test_name']}: {test['message']}")
        
        # Save detailed report
        report_file = f"socketio_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            # Convert datetime objects to strings for JSON serialization
            serializable_events = []
            for event_name, data, timestamp in self.received_events:
                serializable_events.append({
                    'event_name': event_name,
                    'data': data,
                    'timestamp': timestamp.isoformat()
                })
            
            report_data = {
                **self.results,
                'events_received': serializable_events
            }
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")

    def run_all_tests(self):
        """Run all SocketIO tests"""
        print("🚀 Starting Comprehensive SocketIO Testing...")
        print(f"🎯 Target: {self.server_url}")
        
        start_time = time.time()
        
        # Setup
        self.setup_authentication()
        
        # Run test suites
        self.test_basic_connection()
        self.test_authenticated_connection()
        self.test_ping_pong()
        self.test_subscription_events()
        self.test_heartbeat()
        self.test_broadcast_events()
        self.test_multiple_connections()
        self.test_socketio_api_endpoints()
        
        total_time = time.time() - start_time
        print(f"\n⏱️  Total testing time: {total_time:.2f} seconds")
        
        # Generate report
        self.generate_report()

if __name__ == "__main__":
    tester = SocketIOTester()
    tester.run_all_tests()
