{"passed": 3, "failed": 5, "total": 8, "details": [{"test_name": "SocketIO Test Page", "status": "PASSED", "message": "Page accessible with SocketIO content", "timestamp": "2025-07-16T00:07:31.960470"}, {"test_name": "SocketIO Connections API", "status": "FAILED", "message": "Endpoint not found - SocketIO routes may not be registered", "timestamp": "2025-07-16T00:07:34.012897"}, {"test_name": "Inventory Broadcast API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:07:36.045803"}, {"test_name": "Price Broadcast API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:07:38.087561"}, {"test_name": "User Notification API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:07:40.132102"}, {"test_name": "Cart Update API", "status": "FAILED", "message": "Endpoint not found", "timestamp": "2025-07-16T00:07:42.178725"}, {"test_name": "SocketIO CORS Setup", "status": "PASSED", "message": "CORS headers present: ['Access-Control-Allow-Origin', 'Access-Control-Allow-Credentials']", "timestamp": "2025-07-16T00:07:44.216667"}, {"test_name": "SocketIO Manager Integration", "status": "PASSED", "message": "Server is responding normally", "timestamp": "2025-07-16T00:07:44.216667"}]}