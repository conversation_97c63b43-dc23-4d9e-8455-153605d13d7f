# API Route Testing Guide

This guide explains how to test all 187 API endpoints in your Allora backend after removing the blueprints system.

## Quick Start

1. **Start your Flask application:**
   ```bash
   cd allora/backend
   python app.py
   ```

2. **Run the comprehensive test script:**
   ```bash
   python test_all_routes.py
   ```

## Test Script Features

### 🎯 **Comprehensive Coverage**
- Tests all 187 API endpoints in your app.py
- Covers all HTTP methods (GET, POST, PUT, DELETE)
- Includes authentication, admin, seller, and guest routes

### 🔐 **Authentication Testing**
- Automatically attempts to create test users
- Tests OAuth endpoints
- Validates protected route access
- Tests admin and seller authentication

### 📊 **Detailed Reporting**
- Real-time progress display with emojis
- Success/failure rates
- Detailed error messages
- JSON report saved to `test_results.json`

### 🛡️ **Smart Error Handling**
- Expects certain error codes (401, 403, 404) for protected routes
- Handles network timeouts gracefully
- Continues testing even if some endpoints fail

## Test Categories

### 1. **Basic Routes (23 routes)**
- Health checks
- Public product listings
- Categories and search
- Static content (sitemap, robots.txt)

### 2. **Authentication Routes (13 routes)**
- User signup/login
- OAuth providers (Google, GitHub, etc.)
- Phone authentication with OTP
- Token refresh and logout
- Admin and seller login

### 3. **Protected Routes (15+ routes)**
- User profiles and addresses
- Wishlists and orders
- Cart management
- Payment methods
- Recommendations

### 4. **Admin Routes (20+ routes)**
- Dashboard and analytics
- User and order management
- Inventory and content management
- Marketplace statistics

### 5. **Seller Routes (15+ routes)**
- Seller registration and dashboard
- Product and order management
- Earnings and analytics
- Store management

### 6. **Product Routes (15+ routes)**
- Product details and variants
- Reviews and images
- Recommendations and similar products
- Batch operations

### 7. **Search Routes (7 routes)**
- Search and autocomplete
- Filters and suggestions
- Complex search queries
- Search health checks

### 8. **Community Routes (5 routes)**
- Community posts and stats
- Trending topics
- Hashtag-based posts

### 9. **Analytics Routes (8 routes)**
- Search analytics
- Performance metrics
- Error logging
- Visual search tracking

### 10. **Support Routes (3 routes)**
- Contact forms
- Support tickets
- Status checks

### 11. **Additional Routes**
- Newsletter subscription
- Inventory management
- Order fulfillment
- Cart and checkout
- Guest sessions
- Webhooks
- Testing utilities

## Usage Options

### Basic Usage
```bash
python test_all_routes.py
```

### Custom Server URL
```bash
python test_all_routes.py --url http://localhost:8000
```

### Verbose Output
```bash
python test_all_routes.py --verbose
```

## Expected Results

### ✅ **Success Indicators**
- **200-204**: Successful responses
- **400**: Bad request (expected for invalid data)
- **401**: Unauthorized (expected for protected routes)
- **403**: Forbidden (expected for admin routes)
- **404**: Not found (expected for non-existent resources)
- **422**: Validation error (expected for invalid input)
- **429**: Rate limited (expected due to rate limiting)

### ❌ **Failure Indicators**
- **500**: Internal server error (indicates code issues)
- **Connection errors**: Server not running
- **Timeout errors**: Server not responding

## Interpreting Results

### Sample Output
```
🚀 Starting Comprehensive API Route Testing
============================================================
Base URL: http://localhost:5000
Started at: 2024-01-15T10:30:00
============================================================
✅ Server is running and responding

🔐 Setting up authentication tokens...
✅ User authentication token obtained

🌐 Testing Basic Routes...
✅ GET / - 200
✅ GET /api/health - 200
✅ GET /api/products - 200
...

📊 TEST RESULTS SUMMARY
============================================================
Total Tests: 187
✅ Passed: 165
❌ Failed: 12
⏭️  Skipped: 10
📈 Success Rate: 88.2%
```

### What Success Looks Like
- **85%+ success rate** is excellent
- Most failures should be authentication-related (401/403)
- No 500 errors (indicates no server crashes)
- All basic routes working (health, products, categories)

## Troubleshooting

### Server Not Running
```
❌ Cannot connect to server at http://localhost:5000
💡 Make sure your Flask app is running:
   cd allora/backend
   python app.py
```

### Database Issues
- Ensure your database is properly configured
- Check database connection in app.py
- Verify all required tables exist

### Authentication Failures
- Check if test user creation is working
- Verify JWT token configuration
- Ensure OAuth settings are correct

### High Failure Rate
- Check server logs for errors
- Verify all dependencies are installed
- Ensure database is accessible
- Check for missing environment variables

## Files Generated

1. **`test_results.json`** - Detailed test results with timestamps
2. **Console output** - Real-time progress and summary

## Next Steps

After running the tests:

1. **Review failed tests** - Focus on 500 errors first
2. **Check server logs** - Look for any error messages
3. **Fix critical issues** - Address database or configuration problems
4. **Re-run tests** - Verify fixes work
5. **Deploy with confidence** - Your API is thoroughly tested!

## Tips for Success

- Run tests on a clean database for consistent results
- Ensure all environment variables are set
- Check that Redis is running (for caching/sessions)
- Verify all required Python packages are installed
- Test with different user roles (admin, seller, regular user)

---

**Happy Testing! 🚀**

Your comprehensive test suite ensures all 187 API endpoints are working correctly after removing the blueprints system.
